import math
import numpy as np
from collections import OrderedDict
from shapely.geometry import Point, LineString
import pyvisgraph as vg
from random import uniform
from networkx.classes.digraph import DiGraph
from networkx.algorithms import dfs_labeled_edges


class UnbiasedTree:
    """
    Unbiased tree for prefix and suffix parts.
    """

    def __init__(self, workspace, buchi, init_state, init_label, segment, para):
        """
        Initialization of the tree.

        :param workspace: workspace object.
        :param buchi: buchi automaton object.
        :param init_state: initial location of the robot.
        :param init_label: label generated by the initial location.
        :param segment: 'prefix' or 'suffix' part.
        :param para: parameters regarding unbiased-sampling method.
        """
        # Workspace related parameters
        self.workspace = workspace.workspace
        self.continuous_workspace = workspace.continuous_workspace
        self.N = workspace.N
        self.M = workspace.M
        self.label_loc = workspace.label_loc
        self.regions = workspace.regions
        self.obstacles = workspace.obs

        self.robot = buchi.number_of_robots

        # Task related parameters
        self.buchi = buchi
        self.accept = self.buchi.buchi_graph.graph['accept']
        self.init = init_state

        # Initializing the tree
        self.unbiased_tree = DiGraph(type='PBA', init=self.init)
        self.unbiased_tree.add_node(self.init, cost=0, label=init_label)

        # TL-RRT* related parameters
        self.ord = 2  # Using L2 norm by default
        self.goals = set()
        self.step_size = para['step_size']
        self.segment = segment
        self.lite = para['is_lite']
        self.dim = 2  # 2D workspace by default

        # Size of the ball used in function near
        uni_v = np.power(np.pi, self.robot * self.dim / 2) / math.gamma(self.robot * self.dim / 2 + 1)
        self.gamma = np.ceil(4 * np.power(1 / uni_v, 1. / (self.dim * self.robot)))

        # Selecting final buchi states
        self.b_final = self.buchi.buchi_graph.graph['accept'][0] if self.segment == 'prefix' else \
            self.buchi.buchi_graph.graph['accept']

        # Probability of selecting q_p_closest
        self.p_closest = para['p_closest']

        # Weight when selecting x_rand
        self.y_rand = para['y_rand']

        # Construct visibility graph for obstacle avoidance checks
        polys = [[vg.Point(x[0], x[1]) for x in poly.exterior.coords[:-1]] for poly in self.obstacles]
        self.g = vg.VisGraph()
        self.g.build(polys, status=False)

    def sample(self):
        """
        sample point from the workspace
        :return: sampled point, tuple
        """
        x_rand = []
        for i in range(self.dim):
            x_rand.append(uniform(0, self.continuous_workspace[i]))

        return tuple(x_rand)

    def nearest(self, x_rand):
        """
        find the nearest class of vertices in the tree
        :param: x_rand randomly sampled point form: single point ()
        :return: nearest class of vertices form: single point ()
        """
        min_dis = math.inf
        q_p_nearest = ()
        for node in self.unbiased_tree.nodes:
            x = node[0]
            dis = np.linalg.norm(np.subtract(x_rand, x), ord=self.ord)
            if dis < min_dis:
                q_p_nearest = node
                min_dis = dis
            elif dis == min_dis:
                q_p_nearest = node
        return q_p_nearest

    def steer(self, x_rand, x_nearest):
        """
        steer
        :param: x_rand randomly sampled point form: single point ()
        :param: x_nearest nearest point in the tree form: single point ()
        :return: new point single point ()
        """
        if np.linalg.norm(np.subtract(x_rand, x_nearest), ord=self.ord) <= self.step_size:
            return x_rand
        else:
            new_point = np.asarray(x_nearest) + self.step_size * (np.subtract(x_rand, x_nearest)) / np.linalg.norm(
                np.subtract(x_rand, x_nearest), ord=self.ord)
            return tuple(new_point)

    def extend(self, q_new, near_nodes, label, obs_check):
        """
        add the new state q_new to the tree
        :param: q_new: new state
        :param: near_nodes: near state
        :param: obs_check: check the line connecting two points are inside the freespace
        :return: the tree after extension
        """
        added = False
        cost = np.inf
        q_min = ()
        # loop over all nodes in near_nodes
        for node in near_nodes:
            if q_new != node and obs_check[(q_new[0], node[0])] and \
                    self.check_transition_b(node[1], self.unbiased_tree.nodes[node]['label'], q_new[1]):
                c = self.unbiased_tree.nodes[node]['cost'] \
                    + np.linalg.norm(np.subtract(q_new[0], node[0]), ord=self.ord)
                if c < cost:
                    added = True
                    q_min = node
                    cost = c
        if added:
            self.unbiased_tree.add_node(q_new, cost=cost, label=label)
            self.unbiased_tree.add_edge(q_min, q_new)
            if self.segment == 'prefix' and q_new[1] in self.accept:
                q_n = list(list(self.unbiased_tree.pred[q_new].keys())[0])
                cost = self.unbiased_tree.nodes[tuple(q_n)]['cost']
                label = self.unbiased_tree.nodes[tuple(q_n)]['label']
                q_n[1] = q_new[1]
                q_n = tuple(q_n)
                if q_n != q_min:
                    self.unbiased_tree.add_node(q_n, cost=cost, label=label)
                    self.unbiased_tree.add_edge(q_min, q_n)
                    self.goals.add(q_n)
            elif self.segment == 'suffix' and self.init[1] == q_new[1]:
                self.goals.add(q_new)
        return added

    def rewire(self, q_new, near_nodes, obs_check):
        """
        :param: q_new: new state
        :param: near_nodes: states returned near
        :param: obs_check: check whether obstacle-free
        :return: the tree after rewiring
        """
        for node in near_nodes:
            if obs_check[(q_new[0], node[0])] \
                    and self.check_transition_b(q_new[1], self.unbiased_tree.nodes[q_new]['label'], node[1]):
                c = self.unbiased_tree.nodes[q_new]['cost'] \
                    + np.linalg.norm(np.subtract(q_new[0], node[0]), ord=self.ord)
                delta_c = self.unbiased_tree.nodes[node]['cost'] - c
                # update the cost of node in the subtree rooted at the rewired node
                if delta_c > 0:
                    if list(self.unbiased_tree.pred[node].keys()):
                        self.unbiased_tree.remove_edge(list(self.unbiased_tree.pred[node].keys())[0], node)
                    self.unbiased_tree.add_edge(q_new, node)
                    edges = dfs_labeled_edges(self.unbiased_tree, source=node)
                    for _, v, d in edges:
                        if d == 'forward':
                            self.unbiased_tree.nodes[v]['cost'] = self.unbiased_tree.nodes[v]['cost'] - delta_c

    def near(self, x_new):
        """
        find the states in the near ball
        :param x_new: new point form: single point
        :return: p_near: near state, form: tuple (mulp, buchi)
        """
        near_nodes = []
        radius = min(self.gamma * np.power(
            np.log(self.unbiased_tree.number_of_nodes() + 1) / self.unbiased_tree.number_of_nodes(),
            1. / (self.dim * self.robot)), self.step_size)
        for node in self.unbiased_tree.nodes:
            if np.linalg.norm(np.subtract(x_new, node[0]), ord=self.ord) <= radius:
                near_nodes.append(node)
        return near_nodes

    def obstacle_check(self, near_node, x_new, label):
        """
        check whether line from x_near to x_new is obstacle-free
        :param near_node: nodes returned by near function
        :param x_new: new position component
        :param label: label of x_new
        :return: a dictionary indicating whether the line connecting two points are obstacle-free
        """

        obs_check = {}
        checked = set()

        for node in near_node:
            # whether the position component of nodes has been checked
            if node[0] in checked:
                continue
            checked.add(node[0])
            obs_check[(tuple(x_new), node[0])] = True
            flag = True  # indicate whether break and jump to outer loop
            # the line connecting two points crosses an obstacle
            for boundary in self.obstacles:
                if LineString([Point(node[0]), Point(x_new)]).intersects(boundary):
                    obs_check[(tuple(x_new), node[0])] = False
                    flag = False
                    break
            # no need to check further
            if not flag:
                continue
            for (region, boundary) in iter(self.regions.items()):
                if LineString([Point(node[0]), Point(x_new)]).intersects(boundary) \
                        and region in self.buchi.useful \
                        and region + '_1' != label \
                        and region + '_1' != self.unbiased_tree.nodes[node]['label']:
                    obs_check[(tuple(x_new), node[0])] = False
                    flag = False
                    break
            # no need to check further
            if not flag:
                continue
        return obs_check

    def get_label(self, x):
        """
        Return the label in continuous space for point x.
        :param x: tuple of float
        The coordinates in the continuous workspace.

        Returns:
        :return str: The label at the specified x location.
        """
        point = Point(x)
        for label, region in self.regions.items():
            if point.within(region):
                return label
        for region in self.obstacles:
            if point.within(region):
                return 'o'
        return ''

    def check_transition_b(self, q_b, x_label, q_b_new):
        """
        check whether q_b -- x_label ---> q_b_new
        :param q_b: buchi state
        :param x_label: label of x
        :param q_b_new: buchi state
        :return True if satisfied
        """
        b_state_succ = self.buchi.buchi_graph.succ[q_b]
        # q_b_new is not the successor of b_state
        if q_b_new not in b_state_succ:
            return False
        # check whether label of x enables the transition
        truth = self.buchi.buchi_graph.edges[(q_b, q_b_new)]['truth']
        if self.check_transition_b_helper(x_label, truth):
            return True

        return False

    def check_transition_b_helper(self, x_label, truth):
        """
        check whether transition enabled with current generated label
        :param x_label: label of the current position
        :param truth: symbol enabling the transition
        :return: true or false
        """
        if truth == '1':
            return True
        # all true propositions should be satisdied
        true_label = [true_label for true_label in truth.keys() if truth[true_label]]
        for label in true_label:
            if label not in x_label:
                return False

        # all fasle propositions should not be satisfied
        false_label = [false_label for false_label in truth.keys() if not truth[false_label]]
        for label in false_label:
            if label in x_label:
                return False

        return True

    def find_path(self, goals):
        """
        find the path backwards
        :param goals: found all goal states
        :return: the path leading to the goal state and the corresponding cost
        """
        paths = OrderedDict()
        for i in range(len(goals)):
            goals = list(goals)
            goal = goals[i]
            path = [goal]
            s = goal
            while s != self.init:
                s = list(self.unbiased_tree.pred[s].keys())[0]
                path.insert(0, s)
            if self.segment == 'prefix':
                paths[i] = [self.unbiased_tree.nodes[goal]['cost'], path]
            elif self.segment == 'suffix':
                path.append(self.init)
                paths[i] = [self.unbiased_tree.nodes[goal]['cost'] + np.linalg.norm(
                    np.subtract(goal[0], self.init[0]), ord=self.ord), path]
        return paths
