# -*- coding: utf-8 -*-
"""
边裂演化 ABAQUS-style 示意图
3 张图直接落地
依赖：numpy、matplotlib、pyvista（pip install pyvista matplotlib）
"""
import numpy as np
import matplotlib.pyplot as plt
import pyvista as pv
import matplotlib.colors as mcolors

# 1. 伪造 FEM 结果：裂纹长度 vs 压下量
reduction = np.linspace(0, 0.35, 36)          # 压下量 0→35 %
crack_len = 0.2 + 0.8 * reduction**1.5 * 1e3  # mm

# 2. 图1：裂纹-压下曲线
fig1, ax1 = plt.subplots(figsize=(5, 3))
ax1.plot(reduction*100, crack_len, lw=2, color='firebrick')
ax1.set_xlabel('Reduction (%)')
ax1.set_ylabel('Crack length (mm)')
ax1.set_title('Edge-Crack Evolution')
ax1.grid(ls=':')
fig1.tight_layout()
fig1.savefig('01_crack_len_curve.png', dpi=300)
plt.close(fig1)

# 3. 图2：伪 2D 应力云图（Matplotlib 版）
# 构造带钢 1/4 模型（对称）：x=宽度方向，y=厚度方向
W, H = 60, 2.4                       # mm
nx, ny = 300, 48
x = np.linspace(0, W, nx)
y = np.linspace(0, H, ny)
X, Y = np.meshgrid(x, y)

# 伪造应力场：边部应力集中 + 裂纹缺口效应
stress = 400 * (1 + 0.5/(1+((X-5)/2)**2) * (Y/H)**0.3)  # MPa

fig2, ax2 = plt.subplots(figsize=(6, 3))
cmap = plt.cm.jet
norm = mcolors.Normalize(vmin=300, vmax=700)
im = ax2.contourf(X, Y, stress, levels=128, cmap=cmap, norm=norm)
ax2.set_xlim(0, 25)               # 只看边部
ax2.set_ylim(0, H)
ax2.set_xlabel('Width (mm)')
ax2.set_ylabel('Thickness (mm)')
ax2.set_title('Pseudo Stress σ (MPa)')
cbar = fig2.colorbar(im, ax=ax2, shrink=0.8)
fig2.tight_layout()
fig2.savefig('02_stress_contour.png', dpi=300)
plt.close(fig2)

# 4. 图3：3D 裂纹形貌（PyVista 画）
# 构造 1/4 带钢，表面加一条“V”型裂纹
plate = pv.Box(bounds=(0, 30, 0, 2.4, 0, 10))  # x,y,z = 宽,厚,轧向
points = plate.points.copy()
# 在 x=5 mm 处刻一条 0.6 mm 深 30° 裂纹
x_crack = 5
depth = 0.6
angle = np.deg2rad(30)
xc = points[:, 0]
yc = points[:, 1]
zc = points[:, 2]
inside = (xc >= x_crack) & (xc <= x_crack + depth/np.tan(angle))
yc[inside] -= (xc[inside] - x_crack) * np.tan(angle)
plate.points = points

plotter = pv.Plotter(off_screen=True)
plotter.add_mesh(plate, scalars=points[:, 1], cmap='coolwarm',
                 show_edges=False, smooth_shading=True)
plotter.camera_position = [(60, 30, 40), (15, 1.2, 5), (0, 1, 0)]
plotter.add_text('Edge-Crack 3D Morphology', font_size=12)
plotter.show(screenshot='03_crack_3d.png', window_size=[1200, 900])
plotter.close()

print('3 张图已生成：01_crack_len_curve.png、02_stress_contour.png、03_crack_3d.png')