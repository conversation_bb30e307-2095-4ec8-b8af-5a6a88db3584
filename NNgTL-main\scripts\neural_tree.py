from random import uniform
from networkx.classes.digraph import DiGraph
from networkx.algorithms import dfs_labeled_edges
import math
import numpy as np
from collections import OrderedDict
from shapely.geometry import Point, LineString
from utils.uniform_geometry import sample_uniform_geometry
from scipy.stats import truncnorm
import pyvisgraph as vg

EMPTY_CELL = 0
OBSTACLE_CELL = 10


def sample_by_prob(prob, x1, y1, x2, y2):
    """
    sample a point from the rectangle determined by (x1,y1) and (x2,y2) according to the probability distribution
    :param prob: probability distribution
    :param x1: x coordinate of the first point
    :param y1: y coordinate of the first point
    :param x2: x coordinate of the second point
    :param y2: y coordinate of the second point
    """
    # get the rectangle
    x_min = min(x1, x2)
    x_max = max(x1, x2)
    y_min = min(y1, y2)
    y_max = max(y1, y2)
    # get the sub-probability distribution
    P = prob[x_min:x_max + 1, y_min:y_max + 1]
    # normalize
    P = P / np.sum(P)
    # sample
    sample = np.random.choice(range(P.size), p=P.flatten())
    # convert to the coordinate
    x = sample // P.shape[1] + x_min
    y = sample % P.shape[1] + y_min
    return x, y


def select_according_to_prob(candidates, prob):
    """
    select a node from candidates according to the predicted probability
    :param candidates: candidate nodes
    :param prob: predicted probability
    :return: selected node
    """
    if len(candidates) == 1:
        return candidates[0]
    else:
        prob = np.asarray(prob)
        prob = prob / np.sum(prob)
        index = np.random.choice(range(prob.size), p=prob.flatten())
        return candidates[index]


class NeuralTree(object):
    """
    Neural tree for prefix and suffix parts.
    """

    def __init__(self, workspace, buchi, init_state, init_label, segment, prob, prob_nodes_dict, para):
        """
        initialization of the tree
        :param workspace: workspace
        :param buchi: buchi automaton
        :param init_state: initial location of the robots
        :param init_label: label generated by the initial location
        :param segment: prefix or suffix part
        :param prob: probability map
        :param prob_nodes_dict: predicted probability of buchi states
        :param para: parameters
        """
        # parameters regarding workspace
        self.workspace = workspace.workspace
        self.continuous_workspace = workspace.continuous_workspace
        self.N = workspace.N
        self.M = workspace.M
        self.label_loc = workspace.label_loc
        self.regions = workspace.regions
        self.obstacles = workspace.obs
        self.Workspace = workspace

        self.robot = buchi.number_of_robots
        # parameters regarding task
        self.buchi = buchi
        self.accept = self.buchi.buchi_graph.graph['accept']
        self.init = init_state

        # feasible accepting state selected according to the predicted probability
        prob_accept = [prob_nodes_dict[accept] for accept in self.accept]
        self.b_final = select_according_to_prob(self.accept, prob_accept)

        # initlizing the tree
        self.neural_tree = DiGraph(type='PBA', init=self.init)
        self.neural_tree.add_node(self.init, cost=0, label=init_label)

        # parameters regarding TL-RRT* algorithm
        self.ord = 2  # Using L2 norm by default
        self.goals = set()
        self.step_size = para['step_size']
        self.segment = segment
        self.lite = para['is_lite']
        self.dim = 2  # 2D workspace by default

        # size of the ball used in function near
        uni_v = np.power(np.pi, self.robot * self.dim / 2) / math.gamma(self.robot * self.dim / 2 + 1)
        self.gamma = np.ceil(4 * np.power(1 / uni_v, 1. / (self.dim * self.robot)))  # unit workspace

        # group the nodes in the tree by the buchi state
        self.group = dict()
        self.add_group(self.init)

        self.min_dis = np.inf
        self.q_min2final = []
        self.not_q_min2final = []
        self.update_min_dis2final_and_partition(self.init)

        # predicted probability map
        self.probability_map = prob

        # predicted probability of nodes
        self.prob_nodes = prob_nodes_dict

        # probability of selecting q_p_closest
        self.p_closest = para['p_closest']
        # weight when selecting x_rand
        self.y_rand = para['y_rand']

        # probability of using neural network guided sampling
        self.p_BA_predict = para['p_BA_predict']

    def nn_guided_sample(self):
        """
        neural network guided biased sample
        :return: sampled point x_rand, closest node q_p_closest in terms of transitions
        """
        # sample nodes as q_p_closest from two partitioned sets
        p_rand = np.random.uniform(0, 1, 1)
        q_p_closest = None
        if (p_rand <= self.p_closest and len(self.q_min2final) > 0) or not self.not_q_min2final:

            candidate_list = self.q_min2final
            prob_list = [self.prob_nodes[q[1]] for q in candidate_list]
            if np.random.uniform(0, 1, 1) < self.p_BA_predict:
                q_p_closest = select_according_to_prob(candidate_list, prob_list)
            else:
                q_p_closest = sample_uniform_geometry(candidate_list)

        elif p_rand > self.p_closest or not self.q_min2final:
            q_p_closest = sample_uniform_geometry(self.not_q_min2final)

        # find the reachable sets of buchi state of q_p_closest
        reachable_q_b_closest = []
        for b_state in self.buchi.buchi_graph.succ[q_p_closest[1]]:
            if self.check_transition_b_helper(self.neural_tree.nodes[q_p_closest]['label'],
                                              self.buchi.buchi_graph.edges[(q_p_closest[1], b_state)]['truth']):
                reachable_q_b_closest.append(b_state)
        # if reachable_q_b_closest is empty
        if not reachable_q_b_closest:
            return [], []

        # collect the buchi states in the reachable set of q_p_closest with minimum distance to the final state
        p_rand = np.random.uniform(0, 1, 1)
        if p_rand <= self.p_BA_predict:
            candidate_list = reachable_q_b_closest
            prob_list = [self.prob_nodes[q] for q in candidate_list]
            b_min_from_q_b_closest = select_according_to_prob(candidate_list, prob_list)
        else:
            b_min_from_q_b_closest = self.get_min2final_from_subset(reachable_q_b_closest)

        # collect the buchi states in the reachable set b_min_from_q_b_closest whose successors is 1 step less from
        # the final state than it is
        reachable_decr = dict()
        m_q_b_closest = []
        for b_state in b_min_from_q_b_closest:
            candidate = []
            for succ in self.buchi.buchi_graph.succ[b_state]:
                if self.buchi.min_length[(b_state, self.b_final)] - 1 == self.buchi.min_length[(succ, self.b_final)] \
                        or succ in self.buchi.buchi_graph.graph['accept']:
                    candidate.append(succ)
            if candidate:
                reachable_decr[b_state] = candidate
                m_q_b_closest.append(b_state)
        # if empty
        if not m_q_b_closest:
            return [], []
        # sample q_b_min and q_b_decr

        p_rand = np.random.uniform(0, 1, 1)
        if p_rand <= self.p_BA_predict:
            candidate_list = m_q_b_closest
            prob_list = [self.prob_nodes[q] for q in candidate_list]
            q_b_min = select_according_to_prob(candidate_list, prob_list)
        else:
            q_b_min = sample_uniform_geometry(m_q_b_closest)
        p_rand = np.random.uniform(0, 1, 1)
        if p_rand <= self.p_BA_predict:
            candidate_list = reachable_decr[q_b_min]
            prob_list = [self.prob_nodes[q] for q in candidate_list]
            q_b_decr = select_according_to_prob(candidate_list, prob_list)
        else:
            q_b_decr = sample_uniform_geometry(reachable_decr[q_b_min])
        # get the guarding symbol
        truth = self.buchi.buchi_graph.edges[(q_b_min, q_b_decr)]['truth']
        x_rand = tuple(q_p_closest[0])
        return self.buchi_guided_sample_by_truthvalue(truth, x_rand, q_p_closest,
                                                      self.neural_tree.nodes[q_p_closest]['label'])

    def biased_sample(self):
        """
        buchi guided biased sample
        :return: sampled point x_rand, closest node q_p_closest in terms of transitions
        """
        # sample nodes as q_p_closest from two partitioned sets
        p_rand = np.random.uniform(0, 1, 1)
        q_p_closest = None
        if (p_rand <= self.p_closest and len(self.q_min2final) > 0) or not self.not_q_min2final:
            q_p_closest = sample_uniform_geometry(self.q_min2final)
        elif p_rand > self.p_closest or not self.q_min2final:
            q_p_closest = sample_uniform_geometry(self.not_q_min2final)

        # find the reachable sets of buchi state of q_p_closest
        reachable_q_b_closest = []
        for b_state in self.buchi.buchi_graph.succ[q_p_closest[1]]:
            if self.check_transition_b_helper(self.neural_tree.nodes[q_p_closest]['label'],
                                              self.buchi.buchi_graph.edges[(q_p_closest[1], b_state)]['truth']):
                reachable_q_b_closest.append(b_state)
        # if reachable_q_b_closest is empty
        if not reachable_q_b_closest:
            return [], []

        # collect the buchi states in the reachable set of q_p_closest with minimum distance to the final state
        b_min_from_q_b_closest = self.get_min2final_from_subset(reachable_q_b_closest)

        # collect the buchi states in the reachable set b_min_from_q_b_closest whose successors is 1 step less from
        # the final state than the it is
        reachable_decr = dict()
        m_q_b_closest = []
        for b_state in b_min_from_q_b_closest:
            candidate = []
            for succ in self.buchi.buchi_graph.succ[b_state]:
                if self.buchi.min_length[(b_state, self.b_final)] - 1 == self.buchi.min_length[(succ, self.b_final)] \
                        or succ in self.buchi.buchi_graph.graph['accept']:
                    candidate.append(succ)
            if candidate:
                reachable_decr[b_state] = candidate
                m_q_b_closest.append(b_state)
        # if empty
        if not m_q_b_closest:
            return [], []
        # sample q_b_min and q_b_decr
        q_b_min = sample_uniform_geometry(m_q_b_closest)
        q_b_decr = sample_uniform_geometry(reachable_decr[q_b_min])
        # get the guarding symbol
        truth = self.buchi.buchi_graph.edges[(q_b_min, q_b_decr)]['truth']
        x_rand = tuple(q_p_closest[0])
        return self.buchi_guided_sample_by_truthvalue(truth, x_rand, q_p_closest,
                                                      self.neural_tree.nodes[q_p_closest]['label'])

    def unbiased_sample(self):
        """
        unbiased sample in the discrete workspace
        :return: sampled point, tuple
        """
        while True:
            x = np.random.randint(0, self.N)
            y = np.random.randint(0, self.M)
            if self.workspace[x][y] != OBSTACLE_CELL:
                x_rand = (x, y)
                return x_rand

    def buchi_guided_sample_by_truthvalue(self, truth, x_rand, q_p_closest, x_label):
        """
        sample a point moving towards the region corresponding to the guarding symbol
        :param truth: guarding symbol that enables the transition
        :param q_p_closest: the node q_p_closest
        :param x_rand: point to be sampled
        :param x_label: label of position of q_p_closest
        :return: sampled point x_rand, q_p_closest
        """
        x_rand = self.Workspace.continuous_to_discrete(x_rand)
        if truth == '1':
            return q_p_closest[0], q_p_closest
        else:
            for key in truth:
                # move towards the target position
                if truth[key] and key not in x_label:
                    pair = key.split('_')  # region-robot pair
                    orig_x_rand = x_rand  # save for further recover
                    while True:
                        x_rand = orig_x_rand  # recover
                        if np.random.uniform(0, 1, 1) <= self.y_rand:
                            # target = self.get_target(orig_x_rand, int(pair[0][1]))
                            goal = tuple(self.label_loc[int(pair[0][1]) - 1])
                            target = goal
                            x_rand = self.prob_guided_towards_target(orig_x_rand, target)
                        else:
                            x_rand = self.unbiased_sample()
                        if self.workspace[x_rand[0]][x_rand[1]] != OBSTACLE_CELL:
                            break
        x_rand = self.Workspace.sample_in_region(x_rand)
        return x_rand, q_p_closest

    def add_group(self, q_p):
        """
        add q_p to the group within which all states have the same buchi state
        :param q_p: a product state
        """
        try:
            self.group[q_p[1]].append(q_p)
        except KeyError:
            self.group[q_p[1]] = [q_p]

    def get_min2final_from_subset(self, subset):
        """
        collect the buchi state from the subset of nodes with minimum distance to the final state
        :param subset: set of nodes
        :return: list of buchi states with minimum distance to the final state
        """
        l_min = np.inf
        b_min = set()
        for b_state in subset:
            if self.buchi.min_length[(b_state, self.b_final)] < l_min:
                l_min = self.buchi.min_length[(b_state, self.b_final)]
                b_min = {b_state}
            elif self.buchi.min_length[(b_state, self.b_final)] == l_min:
                b_min.add(b_state)
        return b_min

    def update_min_dis2final_and_partition(self, q_p_new):
        """
         check whether q_p_new has the buchi component with minimum distance to the final state
         if so, update the set b_min which collects the buchi states with minimum distance to the final state
         and the set q_min2final which collects nodes in the tree with buchi states in b_min
        :param q_p_new: new product state
        """
        # smaller than the current nodes with minimum distance
        if self.buchi.min_length[(q_p_new[1], self.b_final)] < self.min_dis:
            self.min_dis = self.buchi.min_length[(q_p_new[1], self.b_final)]
            self.not_q_min2final = self.not_q_min2final + self.q_min2final
            self.q_min2final = [q_p_new]
        # equivalent to
        elif self.buchi.min_length[(q_p_new[1], self.b_final)] == self.min_dis:
            self.q_min2final = self.q_min2final + [q_p_new]
        # larger than
        else:
            self.not_q_min2final = self.not_q_min2final + [q_p_new]

    def get_truncated_normal(self, mean=0, sd=1, low=0, upp=10):
        """
        truncated normal distribution
        :param mean: mean value
        :param sd: standard deviation
        :param low: lower bound of the random variable
        :param upp: upper bound of the random variable
        :return: value of the random variable
        """
        return truncnorm((low - mean) / sd, (upp - mean) / sd, loc=mean, scale=sd)

    def prob_guided_towards_target(self, x, target):
        # sample a point from the rectangle determined by x and target according to the probability distribution
        a, b = sample_by_prob(self.probability_map, x[0], x[1], target[0], target[1])
        x_rand = (a, b)
        return x_rand

    def nearest(self, x_rand):
        """
        find the nearest class of vertices in the tree
        :param: x_rand randomly sampled point form: single point ()
        :return: nearest class of vertices form: single point ()
        """
        min_dis = math.inf
        q_p_nearest = []
        for node in self.neural_tree.nodes:
            x = node[0]
            dis = np.linalg.norm(np.subtract(x_rand, x), ord=self.ord)
            if dis < min_dis:
                q_p_nearest = [node]
                min_dis = dis
            elif dis == min_dis:
                q_p_nearest.append(node)
        return q_p_nearest

    def steer(self, x_rand, x_nearest):
        """
        steer
        :param: x_rand randomly sampled point form: single point ()
        :param: x_nearest nearest point in the tree form: single point ()
        :return: new point single point ()
        """
        if np.linalg.norm(np.subtract(x_rand, x_nearest), ord=self.ord) <= self.step_size:
            return x_rand
        else:
            new_point = np.asarray(x_nearest) + self.step_size * (np.subtract(x_rand, x_nearest)) / np.linalg.norm(
                np.subtract(x_rand, x_nearest), ord=self.ord)
            return tuple(new_point)

    def extend(self, q_new, near_nodes, label, obs_check):
        """
        add the new state q_new to the tree
        :param: q_new: new state
        :param: near_nodes: near state
        :param: obs_check: check the line connecting two points are inside the freespace
        :return: the tree after extension
        """
        added = False
        cost = np.inf
        q_min = ()
        # loop over all nodes in near_nodes
        for node in near_nodes:
            if q_new != node and obs_check[(q_new[0], node[0])] and \
                    self.check_transition_b(node[1], self.neural_tree.nodes[node]['label'], q_new[1]):
                c = self.neural_tree.nodes[node]['cost'] \
                    + np.linalg.norm(np.subtract(q_new[0], node[0]), ord=self.ord)
                if c < cost:
                    added = True
                    q_min = node
                    cost = c
        if added:
            self.neural_tree.add_node(q_new, cost=cost, label=label)
            self.neural_tree.add_edge(q_min, q_new)
            self.add_group(q_new)
            self.update_min_dis2final_and_partition(q_new)
            if self.segment == 'prefix' and q_new[1] in self.accept:
                q_n = list(list(self.neural_tree.pred[q_new].keys())[0])
                cost = self.neural_tree.nodes[tuple(q_n)]['cost']
                label = self.neural_tree.nodes[tuple(q_n)]['label']
                q_n[1] = q_new[1]
                q_n = tuple(q_n)
                if q_n != q_min:
                    self.neural_tree.add_node(q_n, cost=cost, label=label)
                    self.neural_tree.add_edge(q_min, q_n)
                    self.add_group(q_n)
                    self.update_min_dis2final_and_partition(q_n)
                    self.goals.add(q_n)

            elif self.segment == 'suffix' and self.init[1] == q_new[1]:
                self.goals.add(q_new)
        return added

    def rewire(self, q_new, near_nodes, obs_check):
        """
        :param: q_new: new state
        :param: near_nodes: states returned near
        :param: obs_check: check whether obstacle-free
        :return: the tree after rewiring
        """
        for node in near_nodes:
            if obs_check[(q_new[0], node[0])] \
                    and self.check_transition_b(q_new[1], self.neural_tree.nodes[q_new]['label'], node[1]):
                c = self.neural_tree.nodes[q_new]['cost'] \
                    + np.linalg.norm(np.subtract(q_new[0], node[0]), ord=self.ord)
                delta_c = self.neural_tree.nodes[node]['cost'] - c
                # update the cost of node in the subtree rooted at the rewired node
                if delta_c > 0:
                    if list(self.neural_tree.pred[node].keys()):
                        self.neural_tree.remove_edge(list(self.neural_tree.pred[node].keys())[0], node)
                    self.neural_tree.add_edge(q_new, node)
                    edges = dfs_labeled_edges(self.neural_tree, source=node)
                    for _, v, d in edges:
                        if d == 'forward':
                            self.neural_tree.nodes[v]['cost'] = self.neural_tree.nodes[v]['cost'] - delta_c

    def near(self, x_new):
        """
        find the states in the near ball
        :param x_new: new point form: single point
        :return: p_near: near state, form: tuple (mulp, buchi)
        """
        near_nodes = []
        radius = min(
            self.N * self.gamma * np.power(
                np.log(self.neural_tree.number_of_nodes() + 1) / self.neural_tree.number_of_nodes(),
                1. / (self.dim * self.robot)), self.step_size)
        for node in self.neural_tree.nodes:
            if np.linalg.norm(np.subtract(x_new, node[0]), ord=self.ord) <= radius:
                near_nodes.append(node)
        return near_nodes

    def obstacle_check(self, near_node, x_new, label):
        """
        check whether line from x_near to x_new is obstacle-free
        :param near_node: nodes returned by near function
        :param x_new: new position component
        :param label: label of x_new
        :return: a dictionary indicating whether the line connecting two points are obstacle-free
        """

        obs_check = {}
        checked = set()

        for node in near_node:
            # whether the position component of nodes has been checked
            if node[0] in checked:
                continue
            checked.add(node[0])
            obs_check[(tuple(x_new), node[0])] = True
            flag = True  # indicate whether break and jump to outer loop
            # the line connecting two points crosses an obstacle
            for boundary in self.obstacles:
                if LineString([Point(node[0]), Point(x_new)]).intersects(boundary):
                    obs_check[(tuple(x_new), node[0])] = False
                    flag = False
                    break
            # no need to check further
            if not flag:
                continue
            for (region, boundary) in iter(self.regions.items()):
                if LineString([Point(node[0]), Point(x_new)]).intersects(boundary) \
                        and region in self.buchi.useful \
                        and region + '_1' != label \
                        and region + '_1' != self.neural_tree.nodes[node]['label']:
                    obs_check[(tuple(x_new), node[0])] = False
                    flag = False
                    break
            # no need to check further
            if not flag:
                continue
        return obs_check

    def get_label(self, x):
        """
        return the label of the position component of x in continuous workspace
        """
        point = Point(x)
        for label, region in self.regions.items():
            if point.within(region):
                return label
        for region in self.obstacles:
            if point.within(region):
                return 'o'
        return ''

    def check_transition_b(self, q_b, x_label, q_b_new):
        """
        check whether q_b -- x_label ---> q_b_new
        :param q_b: buchi state
        :param x_label: label of x
        :param q_b_new: buchi state
        :return True if satisfied
        """
        b_state_succ = self.buchi.buchi_graph.succ[q_b]
        # q_b_new is not the successor of b_state
        if q_b_new not in b_state_succ:
            return False
        # check whether label of x enables the transition
        truth = self.buchi.buchi_graph.edges[(q_b, q_b_new)]['truth']
        if self.check_transition_b_helper(x_label, truth):
            return True

        return False

    def check_transition_b_helper(self, x_label, truth):
        """
        check whether transition enabled with current generated label
        :param x_label: label of the current position
        :param truth: symbol enabling the transition
        :return: true or false
        """
        if truth == '1':
            return True
        # all true propositions should be satisdied
        true_label = [true_label for true_label in truth.keys() if truth[true_label]]
        for label in true_label:
            if label not in x_label:
                return False

        # all fasle propositions should not be satisfied
        false_label = [false_label for false_label in truth.keys() if not truth[false_label]]
        for label in false_label:
            if label in x_label:
                return False

        return True

    def find_path(self, goals):
        """
        find the path backwards
        :param goals: found all goal states
        :return: the path leading to the goal state and the corresponding cost
        """
        paths = OrderedDict()
        for i in range(len(goals)):
            goals = list(goals)
            goal = goals[i]
            path = [goal]
            s = goal
            while s != self.init:
                s = list(self.neural_tree.pred[s].keys())[0]
                path.insert(0, s)
            if self.segment == 'prefix':
                paths[i] = [self.neural_tree.nodes[goal]['cost'], path]
            elif self.segment == 'suffix':
                path.append(self.init)
                paths[i] = [self.neural_tree.nodes[goal]['cost'] + np.linalg.norm(
                    np.subtract(goal[0], self.init[0]), ord=self.ord), path]

        return paths
